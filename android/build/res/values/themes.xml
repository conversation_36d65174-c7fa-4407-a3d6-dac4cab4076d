<?xml version="1.0" encoding="utf-8"?>
<resources>

	<style name="GodotAppMainTheme" parent="@android:style/Theme.DeviceDefault.NoActionBar">
		<item name="android:windowDrawsSystemBarBackgrounds">false</item>
		<item name="android:windowSwipeToDismiss">false</item>
	</style>

	<style name="GodotAppSplashTheme" parent="Theme.SplashScreen">
		<!-- Set the splash screen background, animated icon, and animation
   duration. -->
		<item name="android:windowSplashScreenBackground">@mipmap/icon_background</item>

		<!-- Use windowSplashScreenAnimatedIcon to add a drawable or an animated
			 drawable. One of these is required. -->
		<item name="windowSplashScreenAnimatedIcon">@mipmap/icon_foreground</item>

		<!-- Set the theme of the Activity that directly follows your splash
		screen. This is required. -->
		<item name="postSplashScreenTheme">@style/GodotAppMainTheme</item>
	</style>
</resources>
