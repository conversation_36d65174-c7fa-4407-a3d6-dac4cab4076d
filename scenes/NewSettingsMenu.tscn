[gd_scene load_steps=4 format=3 uid="uid://dx0voaqkqxqxq"]

[ext_resource type="Script" path="res://scripts/NewSettingsMenu.gd" id="1_script"]
[ext_resource type="Texture2D" uid="uid://bebx3tdqwiil" path="res://assets/UI MMORPG Dark Templar Wenrexa/UI MMORPG Dark Templar/Main Menu/BQ.png" id="2_bg"]
[ext_resource type="Texture2D" uid="uid://tfrk1jdt7gio" path="res://assets/UI MMORPG Dark Templar Wenrexa/UI MMORPG Dark Templar/Game Menu/Buttons/Button Normal.png" id="3_button"]

[node name="SettingsMenu" type="Control"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
script = ExtResource("1_script")

[node name="Background" type="TextureRect" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
texture = ExtResource("2_bg")
expand_mode = 1
stretch_mode = 6

[node name="ContentContainer" type="VBoxContainer" parent="."]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -300.0
offset_top = -400.0
offset_right = 300.0
offset_bottom = 400.0
grow_horizontal = 2
grow_vertical = 2

[node name="TitleLabel" type="Label" parent="ContentContainer"]
layout_mode = 2
text = "NASTAVENIA"
horizontal_alignment = 1

[node name="Spacer1" type="Control" parent="ContentContainer"]
layout_mode = 2
custom_minimum_size = Vector2(0, 50)

[node name="SettingsContainer" type="VBoxContainer" parent="ContentContainer"]
layout_mode = 2
size_flags_vertical = 3

[node name="MasterVolumeContainer" type="VBoxContainer" parent="ContentContainer/SettingsContainer"]
layout_mode = 2

[node name="MasterVolumeLabel" type="Label" parent="ContentContainer/SettingsContainer/MasterVolumeContainer"]
layout_mode = 2
text = "Hlavná hlasitosť: 100%"

[node name="MasterVolumeSlider" type="HSlider" parent="ContentContainer/SettingsContainer/MasterVolumeContainer"]
layout_mode = 2
custom_minimum_size = Vector2(0, 40)

[node name="Spacer2" type="Control" parent="ContentContainer/SettingsContainer"]
layout_mode = 2
custom_minimum_size = Vector2(0, 30)

[node name="MusicVolumeContainer" type="VBoxContainer" parent="ContentContainer/SettingsContainer"]
layout_mode = 2

[node name="MusicVolumeLabel" type="Label" parent="ContentContainer/SettingsContainer/MusicVolumeContainer"]
layout_mode = 2
text = "Hudba: 100%"

[node name="MusicVolumeSlider" type="HSlider" parent="ContentContainer/SettingsContainer/MusicVolumeContainer"]
layout_mode = 2
custom_minimum_size = Vector2(0, 40)

[node name="Spacer3" type="Control" parent="ContentContainer/SettingsContainer"]
layout_mode = 2
custom_minimum_size = Vector2(0, 30)

[node name="SFXVolumeContainer" type="VBoxContainer" parent="ContentContainer/SettingsContainer"]
layout_mode = 2

[node name="SFXVolumeLabel" type="Label" parent="ContentContainer/SettingsContainer/SFXVolumeContainer"]
layout_mode = 2
text = "Zvukové efekty: 100%"

[node name="SFXVolumeSlider" type="HSlider" parent="ContentContainer/SettingsContainer/SFXVolumeContainer"]
layout_mode = 2
custom_minimum_size = Vector2(0, 40)

[node name="Spacer4" type="Control" parent="ContentContainer/SettingsContainer"]
layout_mode = 2
custom_minimum_size = Vector2(0, 30)

[node name="FullscreenContainer" type="HBoxContainer" parent="ContentContainer/SettingsContainer"]
layout_mode = 2

[node name="FullscreenLabel" type="Label" parent="ContentContainer/SettingsContainer/FullscreenContainer"]
layout_mode = 2
size_flags_horizontal = 3
text = "Celá obrazovka:"
vertical_alignment = 1

[node name="FullscreenButton" type="CheckButton" parent="ContentContainer/SettingsContainer/FullscreenContainer"]
layout_mode = 2

[node name="Spacer5" type="Control" parent="ContentContainer"]
layout_mode = 2
custom_minimum_size = Vector2(0, 50)

[node name="BackButton" type="TextureButton" parent="ContentContainer"]
layout_mode = 2
custom_minimum_size = Vector2(200, 60)
texture_normal = ExtResource("3_button")
stretch_mode = 1

[node name="BackLabel" type="Label" parent="ContentContainer/BackButton"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
text = "SPÄŤ"
horizontal_alignment = 1
vertical_alignment = 1
