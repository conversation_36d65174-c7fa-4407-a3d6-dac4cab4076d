[gd_scene load_steps=9 format=3 uid="uid://tx8vn8qkqxqxq"]

[ext_resource type="Script" path="res://scripts/ThreeLeverssPuzzle.gd" id="1_levers"]
[ext_resource type="Texture2D" path="res://assets/UI MMORPG Dark Templar Wenrexa/UI MMORPG Dark Templar/Game Menu/Big_Panel.png" id="2_panel"]
[ext_resource type="Theme" path="res://themes/DarkTemplarTheme.tres" id="3_theme"]
[ext_resource type="Texture2D" path="res://assets/slnko mesiac paky/slnko.png" id="4_sun"]
[ext_resource type="Texture2D" path="res://assets/slnko mesiac paky/mesiac.png" id="5_moon"]
[ext_resource type="Texture2D" path="res://assets/slnko mesiac paky/hviezda.png" id="6_star"]

[sub_resource type="LabelSettings" id="LabelSettings_title"]
font_size = 24
font_color = Color(0.9, 0.8, 0.6, 1)
outline_size = 2
outline_color = Color(0.2, 0.1, 0.05, 1)
shadow_size = 1
shadow_color = Color(0, 0, 0, 0.8)
shadow_offset = Vector2(2, 2)

[sub_resource type="LabelSettings" id="LabelSettings_quote"]
font_size = 18
font_color = Color(0.8, 0.6, 0.9, 1)
outline_size = 2
outline_color = Color(0.2, 0.1, 0.05, 1)
shadow_size = 1
shadow_color = Color(0, 0, 0, 0.8)
shadow_offset = Vector2(2, 2)

[sub_resource type="LabelSettings" id="LabelSettings_status"]
font_size = 18
font_color = Color(0.8, 0.6, 0.2, 1)
outline_size = 2
outline_color = Color(0.2, 0.1, 0.05, 1)
shadow_size = 1
shadow_color = Color(0, 0, 0, 0.8)
shadow_offset = Vector2(2, 2)

[node name="ThreeLeverssPuzzle" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
theme = ExtResource("3_theme")
script = ExtResource("1_levers")

[node name="Background" type="ColorRect" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
color = Color(0, 0, 0, 0.7)

[node name="PuzzlePanel" type="NinePatchRect" parent="."]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -320.0
offset_top = -400.0
offset_right = 320.0
offset_bottom = 400.0
texture = ExtResource("2_panel")
patch_margin_left = 16
patch_margin_top = 16
patch_margin_right = 16
patch_margin_bottom = 16

[node name="VBoxContainer" type="VBoxContainer" parent="PuzzlePanel"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 20.0
offset_top = 20.0
offset_right = -20.0
offset_bottom = -20.0

[node name="TitleLabel" type="Label" parent="PuzzlePanel/VBoxContainer"]
layout_mode = 2
text = "Tri páky"
label_settings = SubResource("LabelSettings_title")
horizontal_alignment = 1

[node name="Spacer1" type="Control" parent="PuzzlePanel/VBoxContainer"]
custom_minimum_size = Vector2(0, 20)
layout_mode = 2

[node name="DescriptionLabel" type="RichTextLabel" parent="PuzzlePanel/VBoxContainer"]
layout_mode = 2
size_flags_vertical = 3
bbcode_enabled = true
text = "[center]Na stene je vyškriabaný odkaz.[/center]

[b]Úloha:[/b] Postupujte podľa básničky a aktivujte páky v správnom poradí.

Pamätajte si Viktorove slová!"
fit_content = true

[node name="Spacer2" type="Control" parent="PuzzlePanel/VBoxContainer"]
custom_minimum_size = Vector2(0, 20)
layout_mode = 2

[node name="QuoteLabel" type="Label" parent="PuzzlePanel/VBoxContainer"]
layout_mode = 2
text = "\"Kráčaj, kde mesiac svieti, nie tam, kde slnko horí.\nHviezda ti ukáže cestu.\""
label_settings = SubResource("LabelSettings_quote")
horizontal_alignment = 1

[node name="Spacer3" type="Control" parent="PuzzlePanel/VBoxContainer"]
custom_minimum_size = Vector2(0, 30)
layout_mode = 2

[node name="LeversContainer" type="HBoxContainer" parent="PuzzlePanel/VBoxContainer"]
layout_mode = 2
alignment = 1

[node name="SunButton" type="TextureButton" parent="PuzzlePanel/VBoxContainer/LeversContainer"]
custom_minimum_size = Vector2(120, 120)
layout_mode = 2
texture_normal = ExtResource("4_sun")
ignore_texture_size = true
stretch_mode = 5

[node name="Spacer" type="Control" parent="PuzzlePanel/VBoxContainer/LeversContainer"]
custom_minimum_size = Vector2(20, 0)
layout_mode = 2

[node name="MoonButton" type="TextureButton" parent="PuzzlePanel/VBoxContainer/LeversContainer"]
custom_minimum_size = Vector2(120, 120)
layout_mode = 2
texture_normal = ExtResource("5_moon")
ignore_texture_size = true
stretch_mode = 5

[node name="Spacer2" type="Control" parent="PuzzlePanel/VBoxContainer/LeversContainer"]
custom_minimum_size = Vector2(20, 0)
layout_mode = 2

[node name="StarButton" type="TextureButton" parent="PuzzlePanel/VBoxContainer/LeversContainer"]
custom_minimum_size = Vector2(120, 120)
layout_mode = 2
texture_normal = ExtResource("6_star")
ignore_texture_size = true
stretch_mode = 5

[node name="Spacer4" type="Control" parent="PuzzlePanel/VBoxContainer"]
custom_minimum_size = Vector2(0, 30)
layout_mode = 2

[node name="StatusLabel" type="Label" parent="PuzzlePanel/VBoxContainer"]
layout_mode = 2
text = "Postupujte podľa básničky..."
label_settings = SubResource("LabelSettings_status")
horizontal_alignment = 1

[node name="Spacer5" type="Control" parent="PuzzlePanel/VBoxContainer"]
custom_minimum_size = Vector2(0, 20)
layout_mode = 2

[node name="ButtonContainer" type="HBoxContainer" parent="PuzzlePanel/VBoxContainer"]
layout_mode = 2

[node name="HintButton" type="Button" parent="PuzzlePanel/VBoxContainer/ButtonContainer"]
custom_minimum_size = Vector2(0, 50)
layout_mode = 2
size_flags_horizontal = 3
text = "Nápoveda"

[node name="ResetButton" type="Button" parent="PuzzlePanel/VBoxContainer/ButtonContainer"]
custom_minimum_size = Vector2(0, 50)
layout_mode = 2
size_flags_horizontal = 3
text = "Znova"

[node name="CloseButton" type="Button" parent="PuzzlePanel/VBoxContainer/ButtonContainer"]
custom_minimum_size = Vector2(0, 50)
layout_mode = 2
size_flags_horizontal = 3
text = "Zavrieť"
