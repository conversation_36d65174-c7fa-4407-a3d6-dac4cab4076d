extends Control

# Splash Screen s animovaným logom pre "Prekliate Dedičstvo"

@onready var logo: TextureRect = $Logo
@onready var loading_label: Label = $LoadingContainer/LoadingLabel
@onready var fade_overlay: ColorRect = $FadeOverlay

# Animačné nastavenia
var splash_duration: float = 3.0
var fade_in_duration: float = 1.0
var fade_out_duration: float = 1.0
var logo_scale_duration: float = 2.0

func _ready():
	print("🎬 Splash Screen spustený")
	
	# Aplikovanie fontov
	apply_fonts()
	
	# Nastavenie počiatočného stavu
	setup_initial_state()
	
	# Spustenie animácie
	start_splash_animation()

func apply_fonts():
	"""Aplikuje fonty na UI elementy"""
	if loading_label:
		FontLoader.apply_font_style(loading_label, "ui_elements")
		loading_label.add_theme_color_override("font_color", Color("#D4AF37"))
		loading_label.add_theme_font_size_override("font_size", 20)
		
		# Pridaj tieň pre lepšiu čitateľnosť
		loading_label.add_theme_color_override("font_shadow_color", Color.BLACK)
		loading_label.add_theme_constant_override("shadow_offset_x", 2)
		loading_label.add_theme_constant_override("shadow_offset_y", 2)

func setup_initial_state():
	"""Nastaví počiatočný stav pre animáciu"""
	# Logo začína neviditeľné a zmenšené
	logo.modulate.a = 0.0
	logo.scale = Vector2(0.5, 0.5)
	
	# Loading label začína neviditeľný
	loading_label.modulate.a = 0.0
	
	# Fade overlay začína čierny
	fade_overlay.color.a = 1.0

func start_splash_animation():
	"""Spustí hlavnú animáciu splash screen"""
	print("🎭 Spúšťam splash animáciu")

	# 1. Fade in z čiernej (0.5s)
	var fade_tween = create_tween()
	fade_tween.tween_property(fade_overlay, "color:a", 0.0, 0.5)
	await fade_tween.finished

	# 2. Logo fade in + scale up (1.0s)
	var logo_tween = create_tween()
	logo_tween.set_parallel(true)
	logo_tween.tween_property(logo, "modulate:a", 1.0, fade_in_duration)
	logo_tween.tween_property(logo, "scale", Vector2(1.0, 1.0), logo_scale_duration)
	await logo_tween.finished

	# 3. Logo pulse efekt (jemné zväčšenie a zmenšenie)
	create_logo_pulse_animation()

	# 4. Loading label fade in (0.5s)
	await get_tree().create_timer(0.5).timeout
	var loading_tween = create_tween()
	loading_tween.tween_property(loading_label, "modulate:a", 1.0, 0.5)
	await loading_tween.finished

	# 5. Loading dots animácia
	start_loading_dots_animation()

	# 6. Čakanie na dokončenie splash duration
	await get_tree().create_timer(splash_duration - 2.0).timeout

	# 7. Fade out a prechod na main menu
	await fade_to_main_menu()

func create_logo_pulse_animation():
	"""Vytvorí jemný pulse efekt pre logo"""
	var pulse_tween = create_tween()
	pulse_tween.set_loops()  # Nekonečná slučka
	
	# Jemné zväčšenie a zmenšenie
	pulse_tween.tween_property(logo, "scale", Vector2(1.05, 1.05), 1.0)
	pulse_tween.tween_property(logo, "scale", Vector2(1.0, 1.0), 1.0)

func start_loading_dots_animation():
	"""Animuje loading text s bodkami"""
	var dots_count = 0
	var base_text = "Načítava sa"
	
	# Vytvorenie timer pre dots animáciu
	var timer = Timer.new()
	timer.wait_time = 0.5
	timer.timeout.connect(_on_dots_timer_timeout.bind(timer, base_text))
	add_child(timer)
	timer.start()

func _on_dots_timer_timeout(timer: Timer, base_text: String):
	"""Callback pre dots animáciu"""
	var dots_count = (Time.get_ticks_msec() / 500) % 4
	var dots = ""
	for i in range(dots_count):
		dots += "."
	
	loading_label.text = base_text + dots
	
	# Zastaviť timer po určitom čase
	if Time.get_ticks_msec() > 4000:  # 4 sekundy
		timer.queue_free()

func fade_to_main_menu():
	"""Fade out a prechod na main menu"""
	print("🎬 Prechod na Main Menu")
	
	# Fade out animácia
	var fade_tween = create_tween()
	fade_tween.set_parallel(true)
	
	# Logo a loading label fade out
	fade_tween.tween_property(logo, "modulate:a", 0.0, fade_out_duration)
	fade_tween.tween_property(loading_label, "modulate:a", 0.0, fade_out_duration)
	
	# Fade overlay fade in
	fade_tween.tween_property(fade_overlay, "color:a", 1.0, fade_out_duration)
	
	await fade_tween.finished
	
	# Prechod na main menu
	get_tree().change_scene_to_file("res://scenes/MainMenu.tscn")

func _input(event):
	"""Umožní preskočiť splash screen stlačením akéhokoľvek tlačidla"""
	if event.is_pressed():
		print("⏭️ Splash screen preskočený")
		# Okamžitý prechod na main menu
		get_tree().change_scene_to_file("res://scenes/MainMenu.tscn")
