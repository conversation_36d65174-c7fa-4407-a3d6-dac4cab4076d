extends Control

# Jed<PERSON><PERSON><PERSON>ý a funkčný ChaptersMenu script

@onready var title_label: Label = $ContentContainer/TitleLabel
@onready var current_chapter_label: Label = $ContentContainer/MainContainer/ChapterInfo/CurrentChapterLabel
@onready var chapter_title_label: Label = $ContentContainer/MainContainer/ChapterInfo/ChapterTitleLabel
@onready var chapter_description: RichTextLabel = $ContentContainer/MainContainer/ChapterInfo/ChapterDescription
@onready var chapter_image: TextureRect = $ContentContainer/MainContainer/ChapterImageContainer/ChapterImageFrame/ChapterImage
@onready var prev_button: Button = $ContentContainer/NavigationContainer/PrevButton
@onready var next_button: Button = $ContentContainer/NavigationContainer/NextButton
@onready var play_button: Button = $ContentContainer/ButtonsContainer/PlayButton
@onready var back_button: Button = $ContentContainer/ButtonsContainer/BackButton

var current_chapter: int = 1
var max_chapters: int = 7

# <PERSON><PERSON>r<PERSON>zky kapitol
var chapter_images = {
	1: preload("res://assets/Kapitoly_VISUALS/Kapitola_1.png"),
	2: preload("res://assets/Kapitoly_VISUALS/Kapitola_2.png"),
	3: preload("res://assets/Kapitoly_VISUALS/Kapitola_3.png"),
	4: preload("res://assets/Kapitoly_VISUALS/Kapitola_4.png"),
	5: preload("res://assets/Kapitoly_VISUALS/Kapitola_5.png"),
	6: preload("res://assets/Kapitoly_VISUALS/Kapitola_6.png"),
	7: preload("res://assets/Kapitoly_VISUALS/Kapitola_7.png")
}

# Informácie o kapitolách
var chapter_info = {
	1: {
		"title": "CESTA NA ZÁMOK",
		"description": "Marec 1894. Tvoje kolesá sa krútia po blatistej ceste naprieč karpatskými lesmi, kam ťa zavolal posledný telegram od mentora. V tejto úvodnej kapitole začínaš v kolísajúcom kočiari. Silná búrka triešti krajinu na kúsky, kočiš mlčí, a ty čítaš znepokojujúce poznámky Van Helsinga. Krátka hádanka ti odhalí strašnú vetu: Grófka je v krypte. Keď kočiš odmietne pokračovať, si nútený ísť pešo cez temný les, kde sa cesta delí podľa starého verša. Tma houstne, príroda dýcha niečím neľudským."
	},
	2: {
		"title": "BRÁNA ZÁMKU",
		"description": "Pred tebou sa týči zámok – ako mŕtva koruna zabudnutej dynastie. Stojíš pred železnou bránou zdobenou heraldickými symbolmi, pod ktorou tečie voda z topiaceho sa snehu a zo striech ťa pozorujú havrany. Na bráne nájdeš krvavý nápis a lúštiš hádanku, ktorá ti otvorí cestu. Vstupuješ na nádvorie, kde sa z ticha vynorí Viktor – sluha Van Helsinga. Podrobí ťa skúške Rádu Striebornej ruže, kde správne odpovede odomknú tvoju ďalšiu cestu."
	},
	3: {
		"title": "PÁTRANIE V ZÁMKU",
		"description": "Za múrmi vládne ticho. Ale nie je to pokoj. Je to čakanie. Zámok pôsobí opustene, ale zdanlivý pokoj ukrýva staré hrozby. Objavuješ vstupnú halu, čítaš denník, v ktorom Van Helsing priznáva – našiel Isabelle. V knižnici objavuješ skrytú priehradku a lúštiš jednoduchý výpočet, ktorý ťa zavedie hlbšie. Zápisy odhaľujú plán: zostup do katakomb, príprava na boj, a odkaz na vonkajšie múry, kde sa má skrývať tajný vchod do starej časti zámku."
	},
	4: {
		"title": "TAJNÉ KRÍDLO",
		"description": "Vzduch je vlhký a ťažký, ako keby si kráčal útrobami mŕtveho obra. Vstupuješ do starého krídla zámku, kde za stenami cvakajú pasce a mechanizmy. Pomáha ti spomienka na básničku o Mesiaci. Čaká ťa farebný pamäťový test a následne vstupuješ do alchymistického laboratória, kde pripravuješ elixír ochrany. Pred sebou máš zostup do krypt. Čas sa kráti. Isabelle vie, že prichádzaš."
	},
	5: {
		"title": "KRYPTY",
		"description": "Staré schody vedú hlboko pod zem. Vzduch je studený, ako by si zostupoval do srdca smrti. Kamene sú ošúchané, vo vzduchu visí prach a pamäť minulých storočí. Viktor ostáva strážiť. Ty zostupuješ sám. Tiene a čísla ti pomôžu odhaliť kód, ktorý ťa vpustí do pradávnej komnaty. Nájdeš tam lampu, prázdny revolver a zápisník Van Helsinga. Onedlho sa pred tebou objaví sarkofág, zdobený pákami – a v ňom niečo, čo sa ešte stále hýbe."
	},
	6: {
		"title": "KONFRONTÁCIA",
		"description": "Pečať sa láme. Isabelle sa prebúdza. A čas rozhodnutia je tu. Keď otvoríš sarkofág, grófka Isabelle Báthoryová sa prebudí po troch storočiach. Už nie je krásna – je čosi iné, pradávne a znetvorené. Hádanka troch sestier preverí tvoju múdrosť. Ak ju rozlúštiš, máš jedinú šancu vykonať rituál podľa poznámok Van Helsinga. Rytmus svätenej vody a symbolov rozhodne, či zvíťazíš alebo zomrieš v jej tieni."
	},
	7: {
		"title": "ZÁCHRANA MENTORA",
		"description": "Nie všetko je stratené. Ale ani celkom vyhraté. Po porážke Isabelle sa odkryje tajná miestnosť, kde nájdeš Van Helsinga – živého, ale nakazeného. Elixír spomalil premenu, no nie vyliečil. Musíte sa dostať do laboratória v Budapešti. Vychádzaš von, prvé ranné svetlo triešti búrkové mračná, a zámok mizne v diaľke. Dedičstvo rodu Báthoryovcov je zlomené. Ale zápas so zlom nikdy nekončí."
	}
}

func _ready():
	print("ChaptersMenu načítané")
	
	# Pripojenie signálov
	connect_signals()
	
	# Aplikovanie fontov
	apply_fonts()
	
	# Aktualizovanie zobrazenia
	update_chapter_display()
	
	# Nastavenie fokusu
	if play_button:
		play_button.grab_focus()

func connect_signals():
	"""Pripojenie signálov"""
	if prev_button:
		prev_button.pressed.connect(_on_prev_pressed)
	if next_button:
		next_button.pressed.connect(_on_next_pressed)
	if play_button:
		play_button.pressed.connect(_on_play_pressed)
	if back_button:
		back_button.pressed.connect(_on_back_pressed)

func apply_fonts():
	"""Aplikuje fonty na UI elementy"""
	if title_label:
		FontLoader.apply_font_style(title_label, "chapter_title")
		title_label.add_theme_color_override("font_color", Color("#D4AF37"))
		title_label.add_theme_font_size_override("font_size", 36)

	if current_chapter_label:
		FontLoader.apply_font_style(current_chapter_label, "chapter_title")
		current_chapter_label.add_theme_color_override("font_color", Color("#D4AF37"))
		current_chapter_label.add_theme_font_size_override("font_size", 32)
	
	if chapter_title_label:
		FontLoader.apply_font_style(chapter_title_label, "character_dialogue")
		chapter_title_label.add_theme_color_override("font_color", Color("#F5F5DC"))
		chapter_title_label.add_theme_font_size_override("font_size", 24)
	
	if chapter_description:
		FontLoader.apply_font_style(chapter_description, "character_dialogue")
		chapter_description.add_theme_color_override("default_color", Color("#E0E0E0"))
		chapter_description.add_theme_font_size_override("normal_font_size", 18)
	
	# Tlačidlá - už používajú tému automaticky

func update_chapter_display():
	"""Aktualizuje zobrazenie aktuálnej kapitoly"""
	if current_chapter_label:
		current_chapter_label.text = "KAPITOLA " + str(current_chapter)

	if chapter_title_label and chapter_info.has(current_chapter):
		chapter_title_label.text = chapter_info[current_chapter]["title"]

	if chapter_description and chapter_info.has(current_chapter):
		chapter_description.text = chapter_info[current_chapter]["description"]

	# Aktualizovanie obrázka kapitoly
	if chapter_image and chapter_images.has(current_chapter):
		chapter_image.texture = chapter_images[current_chapter]

	# Aktualizovanie tlačidiel
	if prev_button:
		prev_button.disabled = (current_chapter <= 1)
	if next_button:
		next_button.disabled = (current_chapter >= max_chapters)

func _on_prev_pressed():
	if current_chapter > 1:
		current_chapter -= 1
		update_chapter_display()
		AudioManager.play_menu_button_sound()

func _on_next_pressed():
	if current_chapter < max_chapters:
		current_chapter += 1
		update_chapter_display()
		AudioManager.play_menu_button_sound()

func _on_play_pressed():
	print("Spúšťam kapitolu ", current_chapter)
	AudioManager.play_menu_button_sound()
	GameManager.go_to_chapter(current_chapter)

func _on_back_pressed():
	print("Návrat do hlavného menu")
	AudioManager.play_menu_button_sound()
	GameManager.go_to_main_menu()

func _input(event):
	if event.is_action_pressed("ui_cancel"):
		_on_back_pressed()
	elif event.is_action_pressed("ui_left"):
		_on_prev_pressed()
	elif event.is_action_pressed("ui_right"):
		_on_next_pressed()
	elif event.is_action_pressed("ui_accept"):
		_on_play_pressed()
