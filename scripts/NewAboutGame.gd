extends Control

# Jed<PERSON><PERSON><PERSON>ý a funkčný AboutGame script

@onready var title_label: Label = $ContentContainer/TitleLabel
@onready var game_title_label: Label = $ContentContainer/ScrollContainer/GameInfo/GameTitle
@onready var description_label: RichTextLabel = $ContentContainer/ScrollContainer/GameInfo/Description
@onready var back_button: TextureButton = $ContentContainer/BackButton
@onready var back_label: Label = $ContentContainer/BackButton/BackLabel

func _ready():
	print("AboutGame načítané")
	
	# Pripojenie signálov
	if back_button:
		back_button.pressed.connect(_on_back_pressed)
		back_button.grab_focus()
	
	# Aplikovanie fontov
	apply_fonts()

func apply_fonts():
	"""Aplikuje fonty na UI elementy"""
	if title_label:
		FontLoader.apply_font_style(title_label, "chapter_title")
		title_label.add_theme_color_override("font_color", Color("#D4AF37"))
		title_label.add_theme_font_size_override("font_size", 36)

	if game_title_label:
		FontLoader.apply_font_style(game_title_label, "chapter_title")
		game_title_label.add_theme_color_override("font_color", Color("#D4AF37"))
		game_title_label.add_theme_font_size_override("font_size", 28)

	if description_label:
		FontLoader.apply_font_style(description_label, "character_dialogue")
		description_label.add_theme_color_override("default_color", Color("#F5F5DC"))
		description_label.add_theme_font_size_override("normal_font_size", 18)

	if back_label:
		FontLoader.apply_font_style(back_label, "ui_elements")
		back_label.add_theme_color_override("font_color", Color("#D4AF37"))
		back_label.add_theme_font_size_override("font_size", 24)

func _on_back_pressed():
	print("Návrat do hlavného menu")
	AudioManager.play_menu_button_sound()
	GameManager.go_to_main_menu()

func _input(event):
	if event.is_action_pressed("ui_cancel"):
		_on_back_pressed()
