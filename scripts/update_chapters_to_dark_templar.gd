@tool
extends EditorScript

# Skript na hromadnú aktualizáciu Chapter scén na Dark Templar tému
# Spustite v Godot editore cez Tools > Execute Script

func _run():
	print("=== Aktualizujem Chapter scény na Dark Templar tému ===")
	
	# Zoznam Chapter scén na aktualizáciu
	var chapter_scenes = [
		"res://scenes/Chapter2.tscn",
		"res://scenes/Chapter3.tscn",
		"res://scenes/Chapter4.tscn",
		"res://scenes/Chapter5.tscn",
		"res://scenes/Chapter6.tscn",
		"res://scenes/Chapter7.tscn"
	]
	
	for scene_path in chapter_scenes:
		if FileAccess.file_exists(scene_path):
			print("Aktualizujem: ", scene_path)
			update_chapter_theme(scene_path)
		else:
			print("Súbor neexistuje: ", scene_path)
	
	print("=== Hotovo! ===")

func update_chapter_theme(scene_path: String):
	"""Aktualizuje tému v Chapter scéne"""
	var file_content = FileAccess.open(scene_path, FileAccess.READ)
	if file_content == null:
		print("Chyba pri čítaní súboru: ", scene_path)
		return
	
	var content = file_content.get_as_text()
	file_content.close()
	
	# Nahraď GothicTheme za DarkTemplarTheme
	content = content.replace(
		'[ext_resource type="Theme" path="res://themes/GothicTheme.tres"',
		'[ext_resource type="Theme" path="res://themes/DarkTemplarTheme.tres"'
	)
	
	# Uložiť aktualizovaný obsah
	var file_write = FileAccess.open(scene_path, FileAccess.WRITE)
	if file_write == null:
		print("Chyba pri zápise súboru: ", scene_path)
		return
	
	file_write.store_string(content)
	file_write.close()
	
	print("Úspešne aktualizované: ", scene_path)
