@tool
extends EditorScript

# Skript na hromadnú aktualizáciu UI na Dark Templar assety
# Spustite v Godot editore cez Tools > Execute Script

func _run():
	print("=== Aktualizujem UI na Dark Templar assety ===")
	
	# Zoznam puzzle scén na aktualizáciu
	var puzzle_scenes = [
		"res://scenes/NavigationPuzzle.tscn",
		"res://scenes/OrderTestPuzzle.tscn", 
		"res://scenes/ReversedMessagePuzzle.tscn",
		"res://scenes/SimpleCalculationPuzzle.tscn",
		"res://scenes/MemoryTestPuzzle.tscn",
		"res://scenes/ThreeSistersPuzzle.tscn",
		"res://scenes/ThreeLeverssPuzzle.tscn",
		"res://scenes/CaesarCipherPuzzle.tscn",
		"res://scenes/VampireArithmeticPuzzle.tscn",
		"res://scenes/RitualRhythmPuzzle.tscn",
		"res://scenes/ShadowCodePuzzle.tscn"
	]
	
	# Zoznam ostatných scén na aktualizáciu
	var other_scenes = [
		"res://scenes/SettingsMenu.tscn",
		"res://scenes/AboutGame.tscn"
	]
	
	# Aktualizuj puzzle scény
	for scene_path in puzzle_scenes:
		if FileAccess.file_exists(scene_path):
			print("Aktualizujem puzzle: ", scene_path)
			update_puzzle_scene(scene_path)
		else:
			print("Súbor neexistuje: ", scene_path)
	
	# Aktualizuj ostatné scény
	for scene_path in other_scenes:
		if FileAccess.file_exists(scene_path):
			print("Aktualizujem scénu: ", scene_path)
			update_general_scene(scene_path)
		else:
			print("Súbor neexistuje: ", scene_path)
	
	print("=== Hotovo! ===")

func update_puzzle_scene(scene_path: String):
	"""Aktualizuje puzzle scénu s Dark Templar assetmi"""
	var scene = load(scene_path)
	if scene == null:
		print("Chyba pri načítaní scény: ", scene_path)
		return
	
	var scene_instance = scene.instantiate()
	if scene_instance == null:
		print("Chyba pri vytvorení inštancie scény: ", scene_path)
		return
	
	# Aktualizuj tému
	if scene_instance.has_method("set_theme"):
		var dark_templar_theme = load("res://themes/DarkTemplarTheme.tres")
		scene_instance.set_theme(dark_templar_theme)
	
	# Nájdi a aktualizuj UI panely
	update_ui_panels_recursive(scene_instance)
	
	# Uložiť scénu
	var packed_scene = PackedScene.new()
	packed_scene.pack(scene_instance)
	ResourceSaver.save(packed_scene, scene_path)
	
	scene_instance.queue_free()

func update_general_scene(scene_path: String):
	"""Aktualizuje všeobecnú scénu s Dark Templar assetmi"""
	var scene = load(scene_path)
	if scene == null:
		print("Chyba pri načítaní scény: ", scene_path)
		return
	
	var scene_instance = scene.instantiate()
	if scene_instance == null:
		print("Chyba pri vytvorení inštancie scény: ", scene_path)
		return
	
	# Aktualizuj tému
	if scene_instance.has_method("set_theme"):
		var dark_templar_theme = load("res://themes/DarkTemplarTheme.tres")
		scene_instance.set_theme(dark_templar_theme)
	
	# Nájdi a aktualizuj UI komponenty
	update_ui_components_recursive(scene_instance)
	
	# Uložiť scénu
	var packed_scene = PackedScene.new()
	packed_scene.pack(scene_instance)
	ResourceSaver.save(packed_scene, scene_path)
	
	scene_instance.queue_free()

func update_ui_panels_recursive(node: Node):
	"""Rekurzívne aktualizuje UI panely v node"""
	if node is NinePatchRect:
		var nine_patch = node as NinePatchRect
		# Aktualizuj textúru na Dark Templar panel
		var new_texture = load("res://assets/UI MMORPG Dark Templar Wenrexa/UI MMORPG Dark Templar/Game Menu/Big_Panel.png")
		if new_texture:
			nine_patch.texture = new_texture
			nine_patch.patch_margin_left = 25
			nine_patch.patch_margin_top = 25
			nine_patch.patch_margin_right = 25
			nine_patch.patch_margin_bottom = 25
	
	elif node is Button and not node is TextureButton:
		# Konvertuj Button na TextureButton s Dark Templar assetmi
		convert_button_to_texture_button(node)
	
	# Rekurzívne spracuj všetky deti
	for child in node.get_children():
		update_ui_panels_recursive(child)

func update_ui_components_recursive(node: Node):
	"""Rekurzívne aktualizuje všetky UI komponenty"""
	if node is NinePatchRect:
		var nine_patch = node as NinePatchRect
		# Aktualizuj textúru podľa typu panelu
		var new_texture = load("res://assets/UI MMORPG Dark Templar Wenrexa/UI MMORPG Dark Templar/Game Menu/Panel1.png")
		if new_texture:
			nine_patch.texture = new_texture
			nine_patch.patch_margin_left = 20
			nine_patch.patch_margin_top = 20
			nine_patch.patch_margin_right = 20
			nine_patch.patch_margin_bottom = 20
	
	elif node is Button and not node is TextureButton:
		convert_button_to_texture_button(node)
	
	# Rekurzívne spracuj všetky deti
	for child in node.get_children():
		update_ui_components_recursive(child)

func convert_button_to_texture_button(button: Button):
	"""Konvertuje Button na TextureButton s Dark Templar assetmi"""
	var parent = button.get_parent()
	if parent == null:
		return
	
	# Vytvor nový TextureButton
	var texture_button = TextureButton.new()
	texture_button.name = button.name
	
	# Skopíruj vlastnosti
	texture_button.position = button.position
	texture_button.size = button.size
	texture_button.custom_minimum_size = button.custom_minimum_size
	
	# Nastav Dark Templar textúry
	var normal_texture = load("res://assets/UI MMORPG Dark Templar Wenrexa/UI MMORPG Dark Templar/Game Menu/Buttons/Button Normal.png")
	var hover_texture = load("res://assets/UI MMORPG Dark Templar Wenrexa/UI MMORPG Dark Templar/Game Menu/Buttons/Button Hover.png")
	
	if normal_texture:
		texture_button.texture_normal = normal_texture
	if hover_texture:
		texture_button.texture_hover = hover_texture
		texture_button.texture_pressed = hover_texture
	
	texture_button.stretch_mode = TextureButton.STRETCH_KEEP_ASPECT_CENTERED
	
	# Vytvor label pre text
	var label = Label.new()
	label.name = button.name + "Label"
	label.text = button.text
	label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	label.vertical_alignment = VERTICAL_ALIGNMENT_CENTER
	label.anchors_preset = Control.PRESET_FULL_RECT
	
	# Pridaj label ako dieťa texture buttonu
	texture_button.add_child(label)
	
	# Nahraď starý button novým
	var button_index = button.get_index()
	parent.remove_child(button)
	parent.add_child(texture_button)
	parent.move_child(texture_button, button_index)
	
	button.queue_free()
