@tool
extends EditorScript

# Skript na dokončenie aktualizácie zvyšných puzzle scén
# Spustite v Godot editore cez Tools > Execute Script

func _run():
	print("=== Dokončujem aktualizáciu puzzle scén ===")
	
	# Zvyšné scény na aktualizáciu
	var remaining_scenes = [
		"res://scenes/SimpleCalculationPuzzle.tscn",
		"res://scenes/VampireArithmeticPuzzle.tscn",
		"res://scenes/ShadowCodePuzzle.tscn",
		"res://scenes/RitualRhythmPuzzle.tscn"
	]
	
	for scene_path in remaining_scenes:
		if FileAccess.file_exists(scene_path):
			print("Aktualizujem: ", scene_path)
			update_scene_manually(scene_path)
		else:
			print("Súbor neexistuje: ", scene_path)
	
	print("=== Hotovo! ===")

func update_scene_manually(scene_path: String):
	"""Manuálne aktualizuje scénu"""
	var file = FileAccess.open(scene_path, FileAccess.READ)
	if not file:
		print("Chyba pri čítaní: ", scene_path)
		return
	
	var content = file.get_as_text()
	file.close()
	
	# Aktualizácie
	var updated = false
	
	# 1. Nahradiť UI_Pozadie.png za Big_Panel.png
	if "UI_Pozadie.png" in content:
		content = content.replace(
			'path="res://assets/Obrázky/UI_Pozadie.png"',
			'path="res://assets/UI MMORPG Dark Templar Wenrexa/UI MMORPG Dark Templar/Game Menu/Big_Panel.png"'
		)
		updated = true
	
	# 2. Nahradiť GothicTheme za DarkTemplarTheme
	if "GothicTheme.tres" in content:
		content = content.replace(
			'path="res://themes/GothicTheme.tres"',
			'path="res://themes/DarkTemplarTheme.tres"'
		)
		updated = true
	
	# 3. Pridať DarkTemplarTheme ak neexistuje
	if "DarkTemplarTheme.tres" not in content and "Theme" not in content:
		# Zvýšiť load_steps
		content = content.replace("load_steps=3", "load_steps=4")
		
		# Pridať theme import
		var import_pos = content.find('[ext_resource type="Texture2D"')
		if import_pos > 0:
			var theme_import = '[ext_resource type="Theme" path="res://themes/DarkTemplarTheme.tres" id="3_theme"]\n'
			content = content.insert(import_pos, theme_import)
		
		# Pridať theme do root node
		var node_pattern = 'script = ExtResource("1_'
		var node_pos = content.find(node_pattern)
		if node_pos > 0:
			var line_end = content.find('\n', node_pos)
			if line_end > 0:
				content = content.insert(line_end, '\ntheme = ExtResource("3_theme")')
		
		updated = true
	
	# 4. Aktualizovať patch margins
	if "patch_margin_left = 16" in content:
		content = content.replace("patch_margin_left = 16", "patch_margin_left = 25")
		content = content.replace("patch_margin_top = 16", "patch_margin_top = 25")
		content = content.replace("patch_margin_right = 16", "patch_margin_right = 25")
		content = content.replace("patch_margin_bottom = 16", "patch_margin_bottom = 25")
		updated = true
	
	# Uložiť ak boli zmeny
	if updated:
		file = FileAccess.open(scene_path, FileAccess.WRITE)
		if file:
			file.store_string(content)
			file.close()
			print("  ✅ Aktualizované: ", scene_path)
		else:
			print("  ❌ Chyba pri ukladaní: ", scene_path)
	else:
		print("  ℹ️ Žiadne zmeny potrebné: ", scene_path)
