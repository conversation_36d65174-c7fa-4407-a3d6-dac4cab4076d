extends Control

# Jedno<PERSON>chý a funkčný SettingsMenu script

@onready var title_label: Label = $ContentContainer/TitleLabel
@onready var master_volume_slider: HSlider = $ContentContainer/SettingsContainer/MasterVolumeContainer/MasterVolumeSlider
@onready var master_volume_label: Label = $ContentContainer/SettingsContainer/MasterVolumeContainer/MasterVolumeLabel
@onready var music_volume_slider: HSlider = $ContentContainer/SettingsContainer/MusicVolumeContainer/MusicVolumeSlider
@onready var music_volume_label: Label = $ContentContainer/SettingsContainer/MusicVolumeContainer/MusicVolumeLabel
@onready var sfx_volume_slider: HSlider = $ContentContainer/SettingsContainer/SFXVolumeContainer/SFXVolumeSlider
@onready var sfx_volume_label: Label = $ContentContainer/SettingsContainer/SFXVolumeContainer/SFXVolumeLabel
@onready var fullscreen_button: CheckButton = $ContentContainer/SettingsContainer/FullscreenContainer/FullscreenButton
@onready var back_button: TextureButton = $ContentContainer/BackButton
@onready var back_label: Label = $ContentContainer/BackButton/BackLabel

func _ready():
	print("SettingsMenu načítané")
	
	# Nastavenie sliderov
	setup_sliders()
	
	# Pripojenie signálov
	connect_signals()
	
	# Aplikovanie fontov
	apply_fonts()
	
	# Aktualizovanie labelov
	update_volume_labels()
	
	# Nastavenie fokusu
	if master_volume_slider:
		master_volume_slider.grab_focus()

func setup_sliders():
	"""Nastavenie hodnôt sliderov"""
	if master_volume_slider:
		master_volume_slider.min_value = 0.0
		master_volume_slider.max_value = 1.0
		master_volume_slider.step = 0.1
		master_volume_slider.value = GameManager.game_settings.master_volume
	
	if music_volume_slider:
		music_volume_slider.min_value = 0.0
		music_volume_slider.max_value = 1.0
		music_volume_slider.step = 0.1
		music_volume_slider.value = GameManager.game_settings.music_volume

	if sfx_volume_slider:
		sfx_volume_slider.min_value = 0.0
		sfx_volume_slider.max_value = 1.0
		sfx_volume_slider.step = 0.1
		sfx_volume_slider.value = GameManager.game_settings.sfx_volume

	if fullscreen_button:
		fullscreen_button.button_pressed = GameManager.game_settings.fullscreen

func connect_signals():
	"""Pripojenie signálov"""
	if master_volume_slider:
		master_volume_slider.value_changed.connect(_on_master_volume_changed)
	if music_volume_slider:
		music_volume_slider.value_changed.connect(_on_music_volume_changed)
	if sfx_volume_slider:
		sfx_volume_slider.value_changed.connect(_on_sfx_volume_changed)
	if fullscreen_button:
		fullscreen_button.toggled.connect(_on_fullscreen_toggled)
	if back_button:
		back_button.pressed.connect(_on_back_pressed)

func apply_fonts():
	"""Aplikuje fonty na UI elementy"""
	if title_label:
		FontLoader.apply_font_style(title_label, "chapter_title")
		title_label.add_theme_color_override("font_color", Color("#D4AF37"))
		title_label.add_theme_font_size_override("font_size", 36)

	if back_label:
		FontLoader.apply_font_style(back_label, "ui_elements")
		back_label.add_theme_color_override("font_color", Color("#D4AF37"))
		back_label.add_theme_font_size_override("font_size", 24)

	# Volume labely
	for label in [master_volume_label, music_volume_label, sfx_volume_label]:
		if label:
			FontLoader.apply_font_style(label, "ui_elements")
			label.add_theme_color_override("font_color", Color("#F5F5DC"))
			label.add_theme_font_size_override("font_size", 20)

func update_volume_labels():
	"""Aktualizuje texty volume labelov"""
	if master_volume_label and master_volume_slider:
		master_volume_label.text = "Hlavná hlasitosť: " + str(int(master_volume_slider.value * 100)) + "%"
	if music_volume_label and music_volume_slider:
		music_volume_label.text = "Hudba: " + str(int(music_volume_slider.value * 100)) + "%"
	if sfx_volume_label and sfx_volume_slider:
		sfx_volume_label.text = "Zvukové efekty: " + str(int(sfx_volume_slider.value * 100)) + "%"

func _on_master_volume_changed(value: float):
	GameManager.update_setting("master_volume", value)
	update_volume_labels()

func _on_music_volume_changed(value: float):
	GameManager.update_setting("music_volume", value)
	update_volume_labels()

func _on_sfx_volume_changed(value: float):
	GameManager.update_setting("sfx_volume", value)
	update_volume_labels()

func _on_fullscreen_toggled(pressed: bool):
	GameManager.update_setting("fullscreen", pressed)

func _on_back_pressed():
	print("Návrat do hlavného menu")
	AudioManager.play_menu_button_sound()
	GameManager.go_to_main_menu()

func _input(event):
	if event.is_action_pressed("ui_cancel"):
		_on_back_pressed()
