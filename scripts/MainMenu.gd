extends Control

# Button references
@onready var nova_hra_button = $MenuContainer/ButtonContainer/NovaHraButton
@onready var pokracovat_button = $MenuContainer/ButtonContainer/PokracovatButton
@onready var kapitoly_button = $MenuContainer/ButtonContainer/KapitolyButton
@onready var nastavenia_button = $MenuContainer/ButtonContainer/NastaveniaButton
@onready var o_hre_button = $MenuContainer/ButtonContainer/OHreButton

# Label references
@onready var nova_hra_label = $MenuContainer/ButtonContainer/NovaHraButton/NovaHraLabel
@onready var pokracovat_label = $MenuContainer/ButtonContainer/PokracovatButton/PokracovatLabel
@onready var kapitoly_label = $MenuContainer/ButtonContainer/KapitolyButton/KapitolyLabel
@onready var nastavenia_label = $MenuContainer/ButtonContainer/NastaveniaButton/NastaveniaLabel
@onready var o_hre_label = $MenuContainer/ButtonContainer/OHreButton/OHreLabel

# Background and UI elements
@onready var background = $Background
@onready var title_overlay = $TitleOverlay
@onready var logo = $TitleOverlay/Logo

func _ready():
	setup_background()
	setup_logo()
	setup_buttons()
	setup_audio()
	adapt_to_screen_size()

	# Aktualizovať viditeľnosť pokračovať buttonu pri každom načítaní
	update_continue_button_visibility()

func setup_background():
	# Nastav pozadie s Dark Templar assetom
	background.texture = load("res://assets/UI MMORPG Dark Templar Wenrexa/UI MMORPG Dark Templar/Main Menu/BQ.png")
	background.stretch_mode = TextureRect.STRETCH_KEEP_ASPECT_COVERED

func setup_logo():
	# Nastav logo v title overlay
	logo.texture = load("res://assets/logo.png")
	logo.stretch_mode = TextureRect.STRETCH_KEEP_ASPECT_CENTERED

func setup_buttons():
	# Nastav texty labelov
	nova_hra_label.text = "NOVÁ HRA"
	pokracovat_label.text = "POKRAČOVAŤ"
	kapitoly_label.text = "KAPITOLY"
	nastavenia_label.text = "NASTAVENIA"
	o_hre_label.text = "O HRE"

	# Aplikuj fonty na labely
	for label in [nova_hra_label, pokracovat_label, kapitoly_label, nastavenia_label, o_hre_label]:
		setup_label_style(label)

	# Pripoj signály
	nova_hra_button.pressed.connect(_on_nova_hra_pressed)
	pokracovat_button.pressed.connect(_on_pokracovat_pressed)
	kapitoly_button.pressed.connect(_on_kapitoly_pressed)
	nastavenia_button.pressed.connect(_on_nastavenia_pressed)
	o_hre_button.pressed.connect(_on_o_hre_pressed)

	# Skryť/zobraziť pokračovať button podľa toho, či existuje save
	update_continue_button_visibility()

	# Nastaviť fokus na prvé dostupné tlačidlo
	if pokracovat_button.visible:
		pokracovat_button.grab_focus()
	else:
		nova_hra_button.grab_focus()

func setup_label_style(label: Label):
	# Aplikuj font - Menu položky → Cinzel-Regular.ttf
	FontLoader.apply_menu_buttons_font(label)
	label.add_theme_font_size_override("font_size", 24)

	# Nastav farbu textu na zlatú
	label.add_theme_color_override("font_color", Color("#D4AF37"))

	# Pridaj tieň pre lepšiu čitateľnosť
	label.add_theme_color_override("font_shadow_color", Color.BLACK)
	label.add_theme_constant_override("shadow_offset_x", 2)
	label.add_theme_constant_override("shadow_offset_y", 2)

func setup_audio():
	# Spusti main menu hudbu
	if AudioManager:
		AudioManager.play_music("main_menu")

func adapt_to_screen_size():
	var viewport = get_viewport()
	if not viewport or not is_instance_valid(viewport):
		print("⚠️ MainMenu: Viewport nie je dostupný pre adaptáciu veľkosti")
		return

	var screen_size = viewport.get_visible_rect().size

	# Adaptuj veľkosť title overlay a logo
	if screen_size.x < 600:  # Small phone
		title_overlay.offset_left = -300
		title_overlay.offset_right = 300
		title_overlay.offset_top = -350
		title_overlay.offset_bottom = -150

		logo.offset_left = -150
		logo.offset_right = 150
		logo.offset_top = -60
		logo.offset_bottom = 60

		# Menšie buttony a text
		for button in get_buttons():
			button.custom_minimum_size = Vector2(300, 60)
		for label in get_labels():
			label.add_theme_font_size_override("font_size", 18)

	elif screen_size.x > 1200:  # Large screen
		title_overlay.offset_left = -500
		title_overlay.offset_right = 500
		title_overlay.offset_top = -450
		title_overlay.offset_bottom = -150

		logo.offset_left = -250
		logo.offset_right = 250
		logo.offset_top = -90
		logo.offset_bottom = 90

		# Väčšie buttony a text
		for button in get_buttons():
			button.custom_minimum_size = Vector2(500, 100)
		for label in get_labels():
			label.add_theme_font_size_override("font_size", 28)

func get_buttons() -> Array:
	return [nova_hra_button, pokracovat_button, kapitoly_button, nastavenia_button, o_hre_button]

func get_labels() -> Array:
	return [nova_hra_label, pokracovat_label, kapitoly_label, nastavenia_label, o_hre_label]

# Button callbacks
func _on_nova_hra_pressed():
	print("Starting new game...")
	AudioManager.play_menu_button_sound()
	start_new_game_with_fade()

func _on_pokracovat_pressed():
	print("Continuing game...")
	AudioManager.play_menu_button_sound()
	GameManager.continue_game()

func _on_kapitoly_pressed():
	print("Opening chapter selection...")
	AudioManager.play_menu_button_sound()
	get_tree().change_scene_to_file("res://scenes/NewChaptersMenu.tscn")

func _on_nastavenia_pressed():
	print("Opening settings...")
	AudioManager.play_menu_button_sound()
	get_tree().change_scene_to_file("res://scenes/NewSettingsMenu.tscn")

func _on_o_hre_pressed():
	print("Opening about...")
	AudioManager.play_menu_button_sound()
	get_tree().change_scene_to_file("res://scenes/NewAboutGame.tscn")

func start_new_game_with_fade():
	"""Spustí novú hru s fade efektom a zvukom začiatku"""
	# Vytvor fade overlay
	var fade_overlay = ColorRect.new()
	fade_overlay.color = Color.BLACK
	fade_overlay.color.a = 0.0
	fade_overlay.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
	add_child(fade_overlay)

	# Fade to black
	var tween = create_tween()
	tween.tween_property(fade_overlay, "color:a", 1.0, 1.0)
	await tween.finished

	# Prehrať zvuk začiatku počas fade
	AudioManager.play_game_start_sound()

	# Krátka pauza pre zvuk
	await get_tree().create_timer(0.5).timeout

	# Spustiť novú hru cez GameManager
	GameManager.go_to_chapter(1)

func update_continue_button_visibility():
	"""Aktualizuje viditeľnosť pokračovať buttonu podľa existencie save súboru"""
	if pokracovat_button:
		pokracovat_button.visible = GameManager.has_save_game()
		print("Continue button visibility: ", pokracovat_button.visible)
