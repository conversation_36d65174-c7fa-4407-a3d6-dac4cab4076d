@tool
extends EditorScript

# Skript na hromadnú aktualizáciu všetkých puzzle scén na Dark Templar dizajn
# <PERSON><PERSON>tite v Godot editore cez Tools > Execute Script

func _run():
	print("=== Aktualizujem všetky puzzle scény na Dark Templar dizajn ===")
	
	# Zoznam všetkých puzzle scén na aktualizáciu
	var puzzle_scenes = [
		"res://scenes/CaesarCipherPuzzle.tscn",
		"res://scenes/NavigationPuzzle.tscn",
		"res://scenes/OrderTestPuzzle.tscn", 
		"res://scenes/ReversedMessagePuzzle.tscn",
		"res://scenes/SimpleCalculationPuzzle.tscn",
		"res://scenes/VampireArithmeticPuzzle.tscn",
		"res://scenes/ShadowCodePuzzle.tscn",
		"res://scenes/RitualRhythmPuzzle.tscn"
		# BloodInscriptionPuzzle, MemoryTestPuzzle, ThreeSistersPuzzle, ThreeLeverssPuzzle už majú správny dizajn
	]
	
	for scene_path in puzzle_scenes:
		if FileAccess.file_exists(scene_path):
			print("Aktualizujem puzzle: ", scene_path)
			update_puzzle_scene(scene_path)
		else:
			print("Súbor neexistuje: ", scene_path)
	
	print("=== Hotovo! ===")

func update_puzzle_scene(scene_path: String):
	"""Aktualizuje puzzle scénu na Dark Templar dizajn"""
	var file = FileAccess.open(scene_path, FileAccess.READ)
	if not file:
		print("Chyba pri čítaní súboru: ", scene_path)
		return
	
	var content = file.get_as_text()
	file.close()
	
	# 1. Nahradiť starý papierový panel za Dark Templar panel
	content = content.replace(
		'path="res://assets/Obrázky/UI_Pozadie.png"',
		'path="res://assets/UI MMORPG Dark Templar Wenrexa/UI MMORPG Dark Templar/Game Menu/Big_Panel.png"'
	)
	
	# 2. Nahradiť GothicTheme za DarkTemplarTheme
	content = content.replace(
		'path="res://themes/GothicTheme.tres"',
		'path="res://themes/DarkTemplarTheme.tres"'
	)
	
	# 3. Pridať patch margins pre NinePatchRect (ak nie sú)
	if "patch_margin_left" not in content:
		# Nájdi NinePatchRect sekciu a pridaj patch margins
		var ninepatch_regex = RegEx.new()
		ninepatch_regex.compile(r'(\[node name="PuzzlePanel" type="NinePatchRect"[^\[]*?)(\[)')
		var result = ninepatch_regex.search(content)
		
		if result:
			var ninepatch_section = result.get_string(1)
			var patch_margins = """patch_margin_left = 25
patch_margin_top = 25
patch_margin_right = 25
patch_margin_bottom = 25

"""
			var new_section = ninepatch_section + patch_margins
			content = content.replace(ninepatch_section, new_section)
	
	# 4. Aktualizovať TextureButton na Button (ak existujú)
	content = update_texture_buttons_to_buttons(content)
	
	# Uložiť aktualizovaný obsah
	file = FileAccess.open(scene_path, FileAccess.WRITE)
	if file:
		file.store_string(content)
		file.close()
		print("  ✅ Aktualizované: ", scene_path)
	else:
		print("  ❌ Chyba pri ukladaní: ", scene_path)

func update_texture_buttons_to_buttons(content: String) -> String:
	"""Konvertuje TextureButton na Button pre jednoduchší dizajn"""
	# Nahradiť TextureButton za Button
	content = content.replace('type="TextureButton"', 'type="Button"')
	
	# Odstrániť texture properties
	var texture_properties = [
		"texture_normal",
		"texture_hover", 
		"texture_pressed",
		"texture_focused",
		"stretch_mode"
	]
	
	for property in texture_properties:
		var regex = RegEx.new()
		regex.compile(property + r' = [^\n]*\n')
		content = regex.sub(content, "", true)
	
	return content
