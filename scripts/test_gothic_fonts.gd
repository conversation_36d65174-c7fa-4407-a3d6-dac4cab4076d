extends Control

# Test scéna pre gotické fonty
# Zobrazuje všetky typy fontov v jednej scéne

@onready var container: VBoxContainer = $VBoxContainer

# Import font utilities
const FontLoader = preload("res://scripts/font_loader.gd")

func _ready():
	create_font_samples()

func create_font_samples():
	print("=== TESTOVANIE GOTICKÝCH FONTOV ===")

	# Načítanie font settings
	var chapter_settings = load("res://themes/ChapterTitleSettings.tres")
	var dialogue_settings = load("res://themes/DialogueSettings.tres")
	var narrator_settings = load("res://themes/NarratorSettings.tres")
	var puzzle_settings = load("res://themes/PuzzleSettings.tres")

	# Vytvorenie ukážok s FontLoader
	create_sample_label_with_font("KAPITOLA 1: PRÍCHOD DO ZÁMKU", "chapter_title", "Cinzel - Nadpisy kapitol")
	create_sample_label_with_font("Viktor: 'Vitajte v zámku, pán doktor.'", "character_dialogue", "Cormorant Garamond - Dialógy postav")
	create_sample_label_with_font("Rozprávač: Vstupujete do temnej chodby...", "narrator_text", "Crimson Text - Rozprávačský text")
	create_sample_label_with_font("Začať hru", "ui_elements", "Montserrat - UI elementy")
	create_sample_label_with_font("Ancient Inscription", "puzzle_text", "Uncial Antiqua - Hádanky a záhady")

	# Responzívne testovanie
	test_responsive_scaling()

func create_sample_label_with_font(text: String, font_type: String, description: String):
	# Popis fontu
	var desc_label = Label.new()
	desc_label.text = description
	desc_label.add_theme_color_override("font_color", Color.YELLOW)
	container.add_child(desc_label)

	# Ukážka textu s FontLoader
	var sample_label = Label.new()
	sample_label.text = text
	FontLoader.apply_font_style(sample_label, font_type)
	sample_label.autowrap_mode = TextServer.AUTOWRAP_WORD_SMART
	container.add_child(sample_label)

	# Oddeľovač
	var spacer = Control.new()
	spacer.custom_minimum_size = Vector2(0, 20)
	container.add_child(spacer)

func test_responsive_scaling():
	print("Testovanie responzívneho škálovania...")

	var viewport = get_viewport()
	if not viewport or not is_instance_valid(viewport):
		print("⚠️ TestGothicFonts: Viewport nie je dostupný")
		return

	var screen_size = viewport.get_visible_rect().size
	print("Veľkosť obrazovky: ", screen_size)
	
	if screen_size.x <= 480:
		print("Mobilné zariadenie - aplikujem 80% škálovanie")
	elif screen_size.x <= 768:
		print("Tablet - aplikujem 90% škálovanie")
	else:
		print("Desktop - normálne škálovanie")

func _input(event):
	if event.is_action_pressed("ui_cancel"):
		get_tree().quit()
