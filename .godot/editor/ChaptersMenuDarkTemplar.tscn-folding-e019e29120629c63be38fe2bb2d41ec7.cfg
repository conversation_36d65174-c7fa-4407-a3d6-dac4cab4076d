[folding]

node_unfolds=[Node<PERSON><PERSON>("."), PackedStringArray("Layout"), NodePath("Background"), PackedStringArray("Layout"), NodePath("Background/MainContainer"), PackedStringArray("Layout"), <PERSON>de<PERSON>ath("Background/MainContainer/ContentArea"), PackedStringArray("Layout"), NodePath("Background/MainContainer/ContentArea/ChapterListContainer"), PackedStringArray("Layout"), NodePath("Background/MainContainer/BottomButtonsContainer"), PackedStringArray("Layout"), NodePath("Background/SafeArea_MainContainer_ContentArea_ChapterListContainer_ChapterButtons_Chapter1Button#Chapter1Label"), PackedStringArray("Layout")]
resource_unfolds=["res://scenes/ChaptersMenuDarkTemplar.tscn::LabelSettings_title", PackedStringArray("Resource", "Font", "Outline", "Shadow"), "res://scenes/ChaptersMenuDarkTemplar.tscn::LabelSettings_chapter", PackedStringArray("Resource", "Font", "Outline")]
nodes_folded=[]
