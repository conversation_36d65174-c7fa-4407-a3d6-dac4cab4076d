[folding]

node_unfolds=[NodePath("."), PackedStringArray("Layout", "Theme"), NodePath("Background"), PackedStringArray("Layout", "Patch Margin"), NodePath("SafeArea"), PackedStringArray("Layout"), NodePath("SafeArea/MainContainer"), PackedStringArray("Layout"), NodePath("SafeArea/MainContainer/Header"), PackedStringArray("Layout"), NodePath("SafeArea/MainContainer/Header/TitleContainer"), PackedStringArray("Layout"), NodePath("SafeArea/MainContainer/Header/TitleContainer/ChapterTitle"), PackedStringArray("Layout"), NodePath("SafeArea/MainContainer/Header/Spacer1"), PackedStringArray("Layout"), NodePath("SafeArea/MainContainer/Header/Divider1"), PackedStringArray("Layout"), NodePath("SafeArea/MainContainer/Header/Spacer2"), PackedStringArray("Layout"), NodePath("SafeArea/MainContainer/ContentArea"), PackedStringArray("Layout"), NodePath("SafeArea/MainContainer/ContentArea/ChapterCard"), PackedStringArray("Layout", "Patch Margin"), NodePath("SafeArea/MainContainer/ContentArea/ChapterCard/CardContent"), PackedStringArray("Layout"), NodePath("SafeArea/MainContainer/ContentArea/ChapterCard/CardContent/ChapterSubtitle"), PackedStringArray("Layout"), NodePath("SafeArea/MainContainer/ContentArea/ChapterCard/CardContent/Spacer3"), PackedStringArray("Layout"), NodePath("SafeArea/MainContainer/ContentArea/ChapterCard/CardContent/PreviewContainer"), PackedStringArray("Layout"), NodePath("SafeArea/MainContainer/ContentArea/ChapterCard/CardContent/PreviewContainer/ChapterPreview"), PackedStringArray("Layout"), NodePath("SafeArea/MainContainer/ContentArea/ChapterCard/CardContent/Spacer4"), PackedStringArray("Layout"), NodePath("SafeArea/MainContainer/ContentArea/ChapterCard/CardContent/ChapterDescription"), PackedStringArray("Layout"), NodePath("SafeArea/MainContainer/ContentArea/ChapterCard/CardContent/Spacer5"), PackedStringArray("Layout"), NodePath("SafeArea/MainContainer/ContentArea/ChapterCard/CardContent/ProgressIndicator"), PackedStringArray("Layout"), NodePath("SafeArea/MainContainer/ContentArea/ChapterCard/CardContent/ProgressIndicator/StatusLabel"), PackedStringArray("Layout"), NodePath("SafeArea/MainContainer/NavigationArea"), PackedStringArray("Layout"), NodePath("SafeArea/MainContainer/NavigationArea/Spacer6"), PackedStringArray("Layout"), NodePath("SafeArea/MainContainer/NavigationArea/NavigationContainer"), PackedStringArray("Layout"), NodePath("SafeArea/MainContainer/NavigationArea/NavigationContainer/PrevButton"), PackedStringArray("Layout", "Textures"), NodePath("SafeArea/MainContainer/NavigationArea/NavigationContainer/PrevButton/PrevLabel"), PackedStringArray("Layout"), NodePath("SafeArea/MainContainer/NavigationArea/NavigationContainer/Spacer"), PackedStringArray("Layout"), NodePath("SafeArea/MainContainer/NavigationArea/NavigationContainer/NextButton"), PackedStringArray("Layout", "Textures"), NodePath("SafeArea/MainContainer/NavigationArea/NavigationContainer/NextButton/NextLabel"), PackedStringArray("Layout"), NodePath("SafeArea/MainContainer/NavigationArea/Spacer7"), PackedStringArray("Layout"), NodePath("SafeArea/MainContainer/NavigationArea/ActionContainer"), PackedStringArray("Layout"), NodePath("SafeArea/MainContainer/NavigationArea/ActionContainer/StartButton"), PackedStringArray("Layout", "Textures"), NodePath("SafeArea/MainContainer/NavigationArea/ActionContainer/StartButton/StartLabel"), PackedStringArray("Layout"), NodePath("SafeArea/MainContainer/NavigationArea/ActionContainer/Spacer8"), PackedStringArray("Layout"), NodePath("SafeArea/MainContainer/NavigationArea/ActionContainer/BackButton"), PackedStringArray("Layout", "Textures"), NodePath("SafeArea/MainContainer/NavigationArea/ActionContainer/BackButton/BackLabel"), PackedStringArray("Layout")]
resource_unfolds=["res://scenes/ChaptersMenuNew.tscn::LabelSettings_title", PackedStringArray("Resource", "Font", "Outline", "Shadow")]
nodes_folded=[]
