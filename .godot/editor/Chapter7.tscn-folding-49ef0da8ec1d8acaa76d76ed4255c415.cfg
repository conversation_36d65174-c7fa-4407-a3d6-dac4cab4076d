[folding]

node_unfolds=[Node<PERSON>ath("."), PackedStringArray("Layout", "Theme"), NodePath("Background"), PackedStringArray("Layout", "Patch Margin"), NodePath("VBoxContainer"), PackedStringArray("Layout"), NodePath("VBoxContainer/ChapterTitle"), PackedStringArray("Layout"), NodePath("VBoxContainer/Spacer1"), PackedStringArray("Layout"), NodePath("VBoxContainer/StartButton"), PackedStringArray("Layout"), NodePath("VBoxContainer/Spacer2"), PackedStringArray("Layout"), NodePath("DialogueSystem"), PackedStringArray("Layout")]
resource_unfolds=["res://scenes/Chapter7.tscn::LabelSettings_title", PackedStringArray("Resource", "Font", "Outline", "Shadow")]
nodes_folded=[]
