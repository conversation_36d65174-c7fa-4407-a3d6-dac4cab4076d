[gd_resource type="AudioBusLayout" format=3 uid="uid://3yrboihumfrv"]

[sub_resource type="AudioEffectEQ" id="AudioEffectEQ_master"]
band_1/freq = 100.0
band_1/gain = -1.5
band_1/q = 0.7
band_3/freq = 3500.0
band_3/gain = 1.0
band_3/q = 0.9
band_4/freq = 10000.0
band_4/gain = 1.5
band_4/q = 0.7

[sub_resource type="AudioEffectCompressor" id="AudioEffectCompressor_master"]
threshold = -12.0
ratio = 3.0
gain = 3.0
attack_us = 20000.0
release_ms = 250.0
mix = 1.0
sidechain = &""

[sub_resource type="AudioEffectLimiter" id="AudioEffectLimiter_master"]
threshold_db = -0.5
ceiling_db = -0.1
soft_clip_db = -0.6
soft_clip_ratio = 0.6

[sub_resource type="AudioEffectReverb" id="AudioEffectReverb_master"]
room_size = 0.5
damping = 0.8
spread = 1.0
hipass = 150.0
lopass = 8000.0
dry = 0.95
wet = 0.05
predelay_ms = 15.0
predelay_feedback = 0.4

[resource]
bus/0/volume_db = -2.0
bus/0/effect_0/effect = SubResource("AudioEffectEQ_master")
bus/0/effect_0/enabled = true
bus/0/effect_1/effect = SubResource("AudioEffectCompressor_master")
bus/0/effect_1/enabled = true
bus/0/effect_2/effect = SubResource("AudioEffectLimiter_master")
bus/0/effect_2/enabled = true
bus/0/effect_3/effect = SubResource("AudioEffectReverb_master")
bus/0/effect_3/enabled = true
bus/1/name = &"Music"
bus/1/solo = false
bus/1/mute = false
bus/1/bypass_fx = false
bus/1/volume_db = -4.0
bus/1/send = &"Master"
bus/2/name = &"SFX"
bus/2/solo = false
bus/2/mute = false
bus/2/bypass_fx = false
bus/2/volume_db = -3.0
bus/2/send = &"Master"
bus/3/name = &"UI"
bus/3/solo = false
bus/3/mute = false
bus/3/bypass_fx = false
bus/3/volume_db = -8.0
bus/3/send = &"Master"
bus/4/name = &"Voice"
bus/4/solo = false
bus/4/mute = false
bus/4/bypass_fx = false
bus/4/volume_db = -0.93951
bus/4/send = &"Master"
