[remap]

importer="texture"
type="CompressedTexture2D"
uid="uid://b4jr84esdnwqu"
path="res://.godot/imported/Button Close Hover.png-d8051d89b5a45f1066d5a20c1855b1cb.ctex"
metadata={
"vram_texture": false
}

[deps]

source_file="res://assets/UI MMORPG Dark Templar Wenrexa/UI MMORPG Dark Templar/Button Close Panel/Button Close Hover.png"
dest_files=["res://.godot/imported/Button Close Hover.png-d8051d89b5a45f1066d5a20c1855b1cb.ctex"]

[params]

compress/mode=0
compress/high_quality=false
compress/lossy_quality=0.7
compress/hdr_compression=1
compress/normal_map=0
compress/channel_pack=0
mipmaps/generate=false
mipmaps/limit=-1
roughness/mode=0
roughness/src_normal=""
process/fix_alpha_border=true
process/premult_alpha=false
process/normal_map_invert_y=false
process/hdr_as_srgb=false
process/hdr_clamp_exposure=false
process/size_limit=0
detect_3d/compress_to=1
