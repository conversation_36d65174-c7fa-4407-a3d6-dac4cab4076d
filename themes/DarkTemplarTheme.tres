[gd_resource type="Theme" load_steps=8 format=3 uid="uid://dor07h2n53sgh"]

[ext_resource type="FontFile" uid="uid://md6m40unc1ik" path="res://assets/UI MMORPG Dark Templar Wenrexa/Font/Berry Rotunda.ttf" id="1_font"]
[ext_resource type="Texture2D" uid="uid://cwgyxdakr37ci" path="res://assets/UI MMORPG Dark Templar Wenrexa/UI MMORPG Dark Templar/Game Menu/Buttons/Button Hover.png" id="3_button_hover"]
[ext_resource type="Texture2D" uid="uid://tfrk1jdt7gio" path="res://assets/UI MMORPG Dark Templar Wenrexa/UI MMORPG Dark Templar/Middle Message/Panel.png" id="5_dialogue_panel"]
[ext_resource type="Texture2D" uid="uid://x0ltngktugqp" path="res://assets/UI MMORPG Dark Templar Wenrexa/UI MMORPG Dark Templar/Game Menu/Sliders/Panel.png" id="8_slider_bg"]

[sub_resource type="StyleBoxTexture" id="StyleBoxTexture_button_hover"]
texture = ExtResource("3_button_hover")
texture_margin_left = 12.0
texture_margin_top = 12.0
texture_margin_right = 12.0
texture_margin_bottom = 12.0

[sub_resource type="StyleBoxTexture" id="StyleBoxTexture_slider_bg"]
texture = ExtResource("8_slider_bg")
texture_margin_left = 8.0
texture_margin_top = 8.0
texture_margin_right = 8.0
texture_margin_bottom = 8.0

[sub_resource type="StyleBoxTexture" id="StyleBoxTexture_dialogue_panel"]
texture = ExtResource("5_dialogue_panel")
texture_margin_left = 25.0
texture_margin_top = 25.0
texture_margin_right = 25.0
texture_margin_bottom = 25.0

[resource]
default_font = ExtResource("1_font")
default_font_size = 18
Button/colors/font_color = Color(0.9, 0.8, 0.6, 1)
Button/colors/font_hover_color = Color(1, 0.9, 0.7, 1)
Button/colors/font_outline_color = Color(0.2, 0.1, 0.05, 1)
Button/colors/font_pressed_color = Color(1, 0.9, 0.7, 1)
Button/constants/outline_size = 2
Button/font_sizes/font_size = 18
Button/styles/hover = SubResource("StyleBoxTexture_button_hover")
Button/styles/pressed = SubResource("StyleBoxTexture_button_hover")
HSlider/styles/grabber_area = SubResource("StyleBoxTexture_slider_bg")
Label/colors/font_color = Color(0.9, 0.8, 0.6, 1)
Label/colors/font_outline_color = Color(0.2, 0.1, 0.05, 1)
Label/colors/font_shadow_color = Color(0, 0, 0, 0.8)
Label/constants/outline_size = 1
Label/constants/shadow_offset_x = 2
Label/constants/shadow_offset_y = 2
Label/font_sizes/font_size = 18
Label/fonts/font = ExtResource("1_font")
LineEdit/colors/font_outline_color = Color(0.2, 0.1, 0.05, 1)
LineEdit/colors/font_selected_color = Color(1, 1, 1, 1)
LineEdit/colors/selection_color = Color(0.6, 0.4, 0.2, 0.5)
LineEdit/constants/outline_size = 1
LineEdit/font_sizes/font_size = 16
PanelContainer/styles/panel = SubResource("StyleBoxTexture_dialogue_panel")
RichTextLabel/colors/default_color = Color(0.9, 0.8, 0.6, 1)
RichTextLabel/colors/font_outline_color = Color(0.2, 0.1, 0.05, 1)
RichTextLabel/colors/font_shadow_color = Color(0, 0, 0, 0.8)
RichTextLabel/constants/outline_size = 1
RichTextLabel/constants/shadow_offset_x = 1
RichTextLabel/constants/shadow_offset_y = 1
RichTextLabel/font_sizes/normal_font_size = 18
RichTextLabel/fonts/normal_font = ExtResource("1_font")
